#\!/bin/bash

# Create config/database.ts
cat > backend/src/config/database.ts << 'DBEOF'
import { Pool } from 'pg';
import { Client } from '@elastic/elasticsearch';

// PostgreSQL configuration
export const pgPool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'parts_broker',
  password: process.env.DB_PASSWORD || 'postgres',
  port: parseInt(process.env.DB_PORT || '5432'),
});

// Elasticsearch configuration
export const esClient = new Client({
  node: process.env.ES_NODE || 'http://localhost:9200',
});

// Test database connections
export async function testConnections() {
  try {
    // Test PostgreSQL
    const pgResult = await pgPool.query('SELECT NOW()');
    console.log('✅ PostgreSQL connected:', pgResult.rows[0].now);

    // Test Elasticsearch
    const esResult = await esClient.ping();
    console.log('✅ Elasticsearch connected');

    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Initialize Elasticsearch indices
export async function initializeElasticsearch() {
  try {
    // Create parts index if it doesn't exist
    const indexExists = await esClient.indices.exists({ index: 'parts' });
    
    if (\!indexExists) {
      await esClient.indices.create({
        index: 'parts',
        body: {
          mappings: {
            properties: {
              id: { type: 'keyword' },
              list_id: { type: 'keyword' },
              part_number: { 
                type: 'text',
                analyzer: 'standard',
                fields: {
                  keyword: { type: 'keyword' },
                  ngram: {
                    type: 'text',
                    analyzer: 'ngram_analyzer'
                  }
                }
              },
              description: { 
                type: 'text',
                analyzer: 'standard'
              },
              manufacturer: { 
                type: 'text',
                analyzer: 'standard',
                fields: {
                  keyword: { type: 'keyword' }
                }
              },
              quantity: { type: 'integer' },
              unit_price: { type: 'float' },
              condition: { type: 'keyword' },
              list_type: { type: 'keyword' },
              contact_id: { type: 'keyword' },
              created_at: { type: 'date' }
            }
          },
          settings: {
            analysis: {
              analyzer: {
                ngram_analyzer: {
                  type: 'custom',
                  tokenizer: 'standard',
                  filter: ['lowercase', 'ngram_filter']
                }
              },
              filter: {
                ngram_filter: {
                  type: 'ngram',
                  min_gram: 2,
                  max_gram: 10
                }
              }
            }
          }
        }
      });
      console.log('✅ Elasticsearch parts index created');
    }
  } catch (error) {
    console.error('❌ Elasticsearch initialization failed:', error);
  }
}
DBEOF

# Create routes/contacts.ts
cat > backend/src/routes/contacts.ts << 'CONTACTSEOF'
import express from 'express';
import { pgPool } from '../config/database';
import { Contact } from '../types';

const router = express.Router();

// Get all contacts
router.get('/', async (req, res) => {
  try {
    const query = \`
      SELECT c.*, 
             COUNT(l.id) as total_lists,
             COUNT(p.id) as total_parts
      FROM contacts c
      LEFT JOIN lists l ON c.id = l.contact_id
      LEFT JOIN parts p ON l.id = p.list_id
      GROUP BY c.id
      ORDER BY c.name
    \`;
    
    const result = await pgPool.query(query);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching contacts:', error);
    res.status(500).json({ error: 'Failed to fetch contacts' });
  }
});

// Create new contact
router.post('/', async (req, res) => {
  try {
    const { name, email, phone, company } = req.body;
    
    if (\!name) {
      return res.status(400).json({ error: 'Name is required' });
    }
    
    const query = \`
      INSERT INTO contacts (name, email, phone, company)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    \`;
    
    const result = await pgPool.query(query, [name, email, phone, company]);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating contact:', error);
    res.status(500).json({ error: 'Failed to create contact' });
  }
});

export default router;
CONTACTSEOF

echo "Backend files created successfully\!"
