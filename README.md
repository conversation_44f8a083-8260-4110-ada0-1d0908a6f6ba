# Electronic Parts Brokerage System

A comprehensive web application for electronic parts brokerage with fuzzy matching capabilities. This system helps brokers automatically detect matches between parts lists from different contacts, enabling efficient parts trading.

## Features

- **Contact Management**: Manage your contact database with companies and individuals
- **CSV Upload & Processing**: Upload parts lists in CSV format with automatic parsing
- **Fuzzy Matching**: Advanced fuzzy matching using Elasticsearch for finding similar parts
- **Multiple List Types**: Support for offer lists, needs lists, and usage lists
- **Price Estimates**: Integration with price estimation data for better prioritization
- **Match Detection**: Automatic detection of matches between new uploads and existing data
- **Web Interface**: Modern Vue.js frontend with Tailwind CSS

## Architecture

- **Frontend**: Vue.js 3 + TypeScript + Tailwind CSS
- **Backend**: Node.js + TypeScript + Express
- **Database**: PostgreSQL for structured data
- **Search Engine**: Elasticsearch for fuzzy matching
- **CSV Processing**: DuckDB for efficient CSV parsing
- **Containerization**: Docker Compose for easy development setup

## Quick Start

### Prerequisites

- <PERSON><PERSON> and Docker Compose
- Node.js 18+ (for local development)
- Git

### 1. Clone and Setup

```bash
git clone <repository-url>
cd parts-broker
```

### 2. Start Infrastructure

```bash
# Start PostgreSQL and Elasticsearch
docker-compose up -d

# Wait for services to be ready (about 30 seconds)
docker-compose logs -f
```

### 3. Setup Backend

```bash
cd backend

# Install dependencies
npm install

# Start development server
npm run dev
```

The backend will be available at `http://localhost:3001`

### 4. Setup Frontend

```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

The frontend will be available at `http://localhost:3000`

### 5. Verify Setup

1. Open `http://localhost:3000` in your browser
2. Check the connection status indicator (should be green)
3. Visit `http://localhost:3001/api/health` to verify backend health

## Usage Guide

### 1. Add Contacts

1. Navigate to "Contacts" in the main menu
2. Click "Add Contact" 
3. Fill in contact information (name is required)
4. Save the contact

### 2. Upload Parts Lists

1. Navigate to "Upload" in the main menu
2. Select a contact from the dropdown
3. Choose the list type:
   - **Offer**: Parts the contact wants to sell
   - **Needs**: Parts the contact wants to buy  
   - **Using**: Parts the contact uses in their products
4. Upload a CSV file with parts data
5. Review the processing results

### 3. CSV Format

Your CSV files should include these columns (column names are auto-detected):

**Required:**
- Part Number: `part_number`, `partnumber`, `part`, `pn`, `mpn`

**Optional:**
- Description: `description`, `desc`, `name`, `title`
- Quantity: `quantity`, `qty`, `amount`, `stock`
- Price: `price`, `unit_price`, `cost`, `value`
- Manufacturer: `manufacturer`, `mfg`, `brand`, `make`
- Condition: `condition`, `state`, `status`

Example CSV:
```csv
Part Number,Description,Quantity,Price,Manufacturer
STM32F407VGT6,32-bit ARM Cortex-M4 MCU,100,12.50,STMicroelectronics
ATMEGA328P-PU,8-bit AVR Microcontroller,50,3.25,Microchip
LM358N,Dual Op-Amp,200,0.85,Texas Instruments
```

### 4. View Matches

After uploading, the system automatically:
1. Parses the CSV file
2. Indexes parts in Elasticsearch
3. Finds fuzzy matches against existing parts
4. Prioritizes matches by score and price estimates

Navigate to "Lists" to view all uploaded lists and their match results.

### 5. Search Parts

Use the "Search" feature to:
- Find parts across all uploaded lists
- Use fuzzy matching for typos and variations
- Filter by list type
- View match confidence scores

## Matching Logic

The system finds matches based on:

### For Offer Lists (selling):
- Matches against **Needs** lists (buyers)
- Matches against **Using** lists (potential buyers)

### For Needs Lists (buying):
- Matches against **Offer** lists (sellers)
- Matches against **Using** lists (potential sellers)

### For Using Lists:
- Matches against **Offer** lists (potential purchases)
- Matches against **Needs** lists (potential sales)
- Matches against other **Using** lists (collaboration opportunities)

### Match Scoring:
- **Exact matches**: 100% confidence
- **High fuzzy**: 80%+ confidence (minor typos)
- **Medium fuzzy**: 60-80% confidence (some variations)
- **Low fuzzy**: Below 60% confidence

Results are prioritized by:
1. Match confidence score
2. Availability of price estimates
3. Recency of upload

## Development

### Backend Structure

```
backend/
├── src/
│   ├── config/         # Database connections
│   ├── routes/         # API endpoints
│   ├── services/       # Business logic
│   ├── types/          # TypeScript types
│   └── index.ts        # Main server file
├── sql/                # Database schema
└── uploads/            # Uploaded files storage
```

### Frontend Structure

```
frontend/
├── src/
│   ├── components/     # Reusable components
│   ├── views/          # Page components
│   ├── services/       # API clients
│   ├── types/          # TypeScript types
│   └── router/         # Vue Router config
└── public/             # Static assets
```

### API Endpoints

- `GET /api/health` - Health check
- `GET /api/contacts` - List contacts
- `POST /api/contacts` - Create contact
- `POST /api/upload/csv` - Upload CSV file
- `GET /api/upload/history` - Upload history
- `GET /api/parts/search` - Search parts
- `GET /api/parts/:id` - Part details

### Database Schema

Key tables:
- `contacts` - Contact information
- `lists` - Uploaded files metadata
- `parts` - Individual parts from lists
- `matches` - Detected matches between parts
- `price_estimates` - Price estimation data

## Configuration

### Environment Variables

Backend (`.env`):
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=parts_broker
DB_USER=postgres
DB_PASSWORD=postgres
ES_NODE=http://localhost:9200
PORT=3001
```

### Docker Services

- **PostgreSQL**: Port 5432, database `parts_broker`
- **Elasticsearch**: Port 9200, single-node setup
- **Kibana**: Port 5601 (for Elasticsearch management)

## Troubleshooting

### Common Issues

1. **Database connection failed**
   - Ensure Docker containers are running: `docker-compose ps`
   - Check logs: `docker-compose logs postgres`

2. **Elasticsearch not responding**
   - Wait for Elasticsearch to fully start (can take 1-2 minutes)
   - Check logs: `docker-compose logs elasticsearch`

3. **File upload fails**
   - Ensure `backend/uploads/` directory exists
   - Check file size (max 10MB)
   - Verify CSV format

4. **No matches found**
   - Check if other lists exist in the database
   - Verify part numbers are not empty
   - Try broader search terms

### Logs

- Backend logs: Console output from `npm run dev`
- Database logs: `docker-compose logs postgres`
- Elasticsearch logs: `docker-compose logs elasticsearch`

## Production Deployment

For production deployment:

1. Set up proper environment variables
2. Use production-grade PostgreSQL and Elasticsearch clusters
3. Configure proper security (authentication, HTTPS)
4. Set up file storage (AWS S3, etc.)
5. Configure monitoring and logging
6. Set up backup strategies

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license information here]
