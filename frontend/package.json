{"name": "parts-broker-frontend", "version": "1.0.0", "description": "Electronic parts brokerage frontend", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "axios": "^1.6.2", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "~5.3.2", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}}