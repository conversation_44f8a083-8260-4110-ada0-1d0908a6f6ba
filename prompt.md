I want to build a web app for electronic part brokerage.
I have contacts either needing parts or wanting to sell parts.
As long as they buy or sell parts, I know they both use them.
They provide me lists (offers or demands, or simply using).
Parts can sometimes be wrongly written in their lists, so I need fuzzy matching.

I need to automatically detect matches between a new list I upload, against previously uploaded lists.

For an offer list, the source contact wants to sell parts. I want to find previously known contacts either looking to buy or using the parts.
For a needs list, the source contact wants to buy parts. I want to find previously known contacts either looking to sell or using the parts.
For a using list, I want to find previously known contacts either looking to buy, sell or using the parts.

In a second step, I'll want to check if I have a price estimate for the part in order to qualify the list, order it by priority, so I know better which lines to manually work harder.

I currently have a PG database. I'm afraid the fuzzy match is too slow. I'm open to reading from PG and pushing data into ElasticSearch.
I'm used to VueJS frontend with Tailwind, NodeJS Typescript backend. I often use DuckDB to parse or generate CSV files.

What would you recommend ?
