-- Initialize database schema for parts broker

-- Contacts table
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    company VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Lists table (uploaded files)
CREATE TABLE lists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contact_id UUID REFERENCES contacts(id),
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    list_type VARCHAR(20) NOT NULL CHECK (list_type IN ('offer', 'needs', 'using')),
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    total_parts INTEGER DEFAULT 0
);

-- Parts table (extracted from lists)
CREATE TABLE parts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    list_id UUID REFERENCES lists(id),
    part_number VARCHAR(255) NOT NULL,
    description TEXT,
    quantity INTEGER,
    unit_price DECIMAL(10,2),
    manufacturer VARCHAR(255),
    condition VARCHAR(50),
    line_number INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Price estimates table
CREATE TABLE price_estimates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    part_number VARCHAR(255) NOT NULL,
    manufacturer VARCHAR(255),
    estimated_price DECIMAL(10,2),
    confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    source VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Matches table (detected matches between parts)
CREATE TABLE matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_part_id UUID REFERENCES parts(id),
    target_part_id UUID REFERENCES parts(id),
    match_score DECIMAL(3,2), -- 0.00 to 1.00
    match_type VARCHAR(50), -- 'exact', 'fuzzy', 'manufacturer_match', etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_parts_part_number ON parts(part_number);
CREATE INDEX idx_parts_list_id ON parts(list_id);
CREATE INDEX idx_lists_contact_id ON lists(contact_id);
CREATE INDEX idx_lists_type ON lists(list_type);
CREATE INDEX idx_price_estimates_part_number ON price_estimates(part_number);
CREATE INDEX idx_matches_source_part ON matches(source_part_id);
CREATE INDEX idx_matches_target_part ON matches(target_part_id);
CREATE INDEX idx_matches_score ON matches(match_score);

-- Insert some sample data
INSERT INTO contacts (name, email, company) VALUES 
('John Smith', '<EMAIL>', 'TechParts Inc'),
('Sarah Johnson', '<EMAIL>', 'ElectroComponents Ltd'),
('Mike Chen', '<EMAIL>', 'Semiconductor Solutions');

-- Sample price estimates
INSERT INTO price_estimates (part_number, manufacturer, estimated_price, confidence_score, source) VALUES
('STM32F407VGT6', 'STMicroelectronics', 12.50, 0.95, 'distributor_api'),
('ATMEGA328P-PU', 'Microchip', 3.25, 0.90, 'market_data'),
('LM358N', 'Texas Instruments', 0.85, 0.88, 'historical_data');
