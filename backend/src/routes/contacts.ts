import express from 'express';
import { pgPool } from '../config/database';
import { Contact } from '../types';

const router = express.Router();

// Get all contacts
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT c.*,
             COUNT(l.id) as total_lists,
             COUNT(p.id) as total_parts
      FROM contacts c
      LEFT JOIN lists l ON c.id = l.contact_id
      LEFT JOIN parts p ON l.id = p.list_id
      GROUP BY c.id
      ORDER BY c.name
    `;

    const result = await pgPool.query(query);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching contacts:', error);
    res.status(500).json({ error: 'Failed to fetch contacts' });
  }
});

// Create new contact
router.post('/', async (req, res) => {
  try {
    const { name, email, phone, company } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }

    const query = `
      INSERT INTO contacts (name, email, phone, company)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await pgPool.query(query, [name, email, phone, company]);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating contact:', error);
    res.status(500).json({ error: 'Failed to create contact' });
  }
});

export default router;
