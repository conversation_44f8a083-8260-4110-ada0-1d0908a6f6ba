export interface Contact {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  created_at: Date;
  updated_at: Date;
}

export interface List {
  id: string;
  contact_id: string;
  filename: string;
  file_path: string;
  list_type: 'offer' | 'needs' | 'using';
  upload_date: Date;
  processed: boolean;
  total_parts: number;
}

export interface Part {
  id: string;
  list_id: string;
  part_number: string;
  description?: string;
  quantity?: number;
  unit_price?: number;
  manufacturer?: string;
  condition?: string;
  line_number: number;
  created_at: Date;
}

export interface PriceEstimate {
  id: string;
  part_number: string;
  manufacturer?: string;
  estimated_price: number;
  confidence_score: number;
  source: string;
  created_at: Date;
  updated_at: Date;
}

export interface Match {
  id: string;
  source_part_id: string;
  target_part_id: string;
  match_score: number;
  match_type: string;
  created_at: Date;
}

export interface MatchResult {
  source_part: Part;
  target_part: Part;
  target_contact: Contact;
  target_list: List;
  match_score: number;
  match_type: string;
  price_estimate?: PriceEstimate;
}

export interface UploadedFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  destination: string;
  filename: string;
  path: string;
  size: number;
}
