version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: parts_broker_postgres
    environment:
      POSTGRES_DB: parts_broker
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - parts_broker_network

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: parts_broker_elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - parts_broker_network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: parts_broker_kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - parts_broker_network

volumes:
  postgres_data:
  elasticsearch_data:

networks:
  parts_broker_network:
    driver: bridge
